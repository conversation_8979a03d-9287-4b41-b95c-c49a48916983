import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Ta<PERSON>, Tab, Box } from '@mui/material'
import React, { useCallback, useEffect, useState } from 'react'
import useStyles from './Header.styles'
import { BrandLogoMob } from '../ui-kit/icons/brand'
import { userHeaderIcon, buyActiveIcon } from '../ui-kit/icons/svg'
import usdIcon1 from '../../components/ui-kit/icons/utils/usd-cash.webp'
import coinIcon1 from '../../components/ui-kit/icons/utils/card-coin2.webp'
import { useUserStore } from '../../store/useUserSlice'
import { PlayerRoutes } from '../../routes'
import {
  useCoinStore,
  usePortalStore,
  useSearchDialogStore,
  useSiteLogoStore,
  useJackpotStore
} from '../../store/store' // eslint-disable-line
import { socket } from '../../utils/socket'
import Signin from '../Modal/Signin'
import Signup from '../Modal/Signup'
import { getLoginToken } from '../../utils/storageUtils'
import { useLocation, useNavigate } from 'react-router-dom'
import { formatPriceWithCommas } from '../../utils/helpers'
import MobileMenu from './MobileMenu/MobileMenu'
import Search from './Search'
import SearchIcon from '@mui/icons-material/Search'
import { useGetProfileMutation, usePostLowGcBonus } from '../../reactQuery'
import ImageRenderer from '../ImageRenderer'
import { customEvent } from '../../utils/optimoveHelper'
import { toast } from 'react-hot-toast'
import { cloneDeep } from 'lodash'
import InsufficientBalancePopup from '../../pages/GamePlay/InsufficientBalancePopup'
import HeaderRibbon from '../../pages/NotFound/HeaderRibbon'
import JackpotWinPopup from '../../pages/Jackpot/components/JackpotWinPopup'
/* eslint-disable multiline-ternary */

const Header = () => {
  const classes = useStyles()
  const location = useLocation()
  const userDetails = useUserStore((state) => state.userDetails)
  const setUserDetails = useUserStore((state) => state.setUserDetails)
  const portalStore = usePortalStore((state) => state)
  const auth = useUserStore((state) => state)
  const navigate = useNavigate()
  const [isScCoinHover, setIsScCoinHover] = useState(false)
  const [isGCHover, setIsGCHover] = useState(false)
  const isAllowedUserAccess = localStorage.getItem('allowedUserAccess')
  const setSearchDialog = useSearchDialogStore((state) => state.setSearchDialog)
  const setCoin = useCoinStore((state) => state.setCoinType)
  const coinType = useCoinStore((state) => state.coinType)
  const [value, setValue] = useState(0)
  const setKycStatus = useUserStore((state) => state.setKycStatus)
  const [lowGCBonusApplicable, setLowGCBonusApplicable] = useState()
  const isGamePlayActive = location.pathname.startsWith('/game-play')
  const logoData = useSiteLogoStore((state) => state)
  useEffect(() => {
    if (coinType === 'GC') {
      setValue(1)
      setCoin('GC')
    } else {
      setValue(0)
      setCoin('SC')
    }
  }, [coinType])

  useEffect(() => {
    if (coinType === 'SC' && userDetails?.userWallet?.totalScCoin < 5) {
      const parameters = {
        sc_balance: userDetails?.userWallet?.totalScCoin
      }
      if (import.meta.env.VITE_NODE_ENV === 'production') {
        customEvent('low_balance', parameters, userDetails?.userId)
      }
    }
  }, [userDetails?.userWallet])

  const walletSocketConnection = useUserStore((state) => state.walletSocketConnection)

  // SETTERS - JACKPOT
  const { setJackpotWin } = useJackpotStore()

  const onCoinUpdate = useCallback(
    function onCoinUpdate(socData) {
      console.log('###WALET_UPDATE', socData)
      setLowGCBonusApplicable(socData?.data?.lowGcBonusApplicable || false)
      // const updatedWallet = { ...userDetails.userWallet }
      const updatedWallet = cloneDeep(userDetails.userWallet)

      let updateData = false

      if (socData?.data?.gcCoin) {
        updatedWallet.gcCoin = socData?.data?.gcCoin
        updateData = true
      }
      if (socData?.data?.scCoin) {
        updatedWallet.totalScCoin = socData?.data?.scCoin
        updateData = true
      }
      if (socData?.data?.wsc) {
        updatedWallet.scCoin = { ...updatedWallet.scCoin, wsc: socData?.data?.wsc }
        updateData = true
      }
      if (socData?.data?.bsc) {
        updatedWallet.scCoin = { ...updatedWallet.scCoin, bsc: socData?.data?.bsc }
        updateData = true
      }
      if (socData?.data?.winDetails) {
        const parameters = socData?.data?.winDetails
        if (import.meta.env.VITE_NODE_ENV === 'production') {
          customEvent('game_win', parameters, userDetails?.userId)
        }
      }
      if (socData?.data?.jackpotWin) {
        setJackpotWin(true, socData?.data?.jackpotWinAmount)
        portalStore.openPortal(
          () => <JackpotWinPopup jackpotWinAmount={socData?.data?.jackpotWinAmount} userName={userDetails?.username} />,
          'jackpotWin'
        )
        setTimeout(() => {
          setJackpotWin(false, 0)
          portalStore.closePortal()
        }, 8000)
      }
      if (updateData) {
        setUserDetails({ ...userDetails, userWallet: updatedWallet })
      }
    },
    [userDetails]
  )

  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      auth.setUserDetails(res?.data?.data)
      auth.setIsAuthenticate(true)
    },
    onError: (error) => {
      console.log('error', error)
    }
  })
  const onInsufficientBalance = (socData) => {
    if (socData?.data?.insufficientBalance) {
      portalStore.openPortal(() => <InsufficientBalancePopup />, 'purchasePopupModal')
    }
  }

  const onKycUpdate = useCallback(
    function onKycUpdate(socData) {
      const userDetails = useUserStore((state) => state.userDetails)
      const setUserDetails = useUserStore((state) => state.setUserDetails)
      if (socData?.data?.kycStatus === 'K4' || socData?.data?.kycStatus === 'K5') {
        userDetails.kycStatus = socData?.data?.kycStatus
        userDetails.sumsubKycStatus = socData?.data?.sumsubKycStatus
        setUserDetails(userDetails)
      }
      if (socData?.data?.identityVerification === true && socData?.data?.reviewStatus === 'completed') {
        setKycStatus(false)
      }
    },
    [userDetails]
  )

  useEffect(() => {
    if (walletSocketConnection) {
      socket.on('USER_WALLET_BALANCE', onCoinUpdate)
      socket.on('KYC_STATUS_UPDATE', onKycUpdate)
      socket.on('INSUFFICIENT_BALANCE', onInsufficientBalance)
    }
  }, [walletSocketConnection, userDetails])

  // Low GC Coin
  const getLowGCMutation = usePostLowGcBonus({
    onSuccess: (res) => {
      if (res.data.message === 'GC Bonus Credited Successfully') {
        toast.success('Low GC Bonus Added')

        getProfileMutation.mutate()
      } else if (res.data.message === 'Bonus Not found') {
        return null
      }
    },
    onError: (error) => {
      console.log('error', error)
    }
  })

  useEffect(() => {
    if (
      userDetails?.isTermsAccepted &&
      userDetails?.isWelcomeBonusClaimed &&
      userDetails?.isDailyBonusClaimed &&
      lowGCBonusApplicable
    ) {
      getLowGCMutation.mutate()
    }
  }, [userDetails])

  const handleLoginOpen = () => {
    portalStore.openPortal(() => <Signin />, 'loginModal')
  }
  const handleUsernameOpen = () => {
    portalStore.openPortal(() => <Signup />, 'loginModal')
  }
  function a11yProps(index) {
    return {
      id: `simple-tab-${index}`,
      'aria-controls': `simple-tabpanel-${index}`
    }
  }

  const handleChange = (event, newValue) => {
    setValue(newValue)
    if (newValue === 1) {
      setCoin('GC')
      setIsGCHover(true)
    } else {
      setCoin('SC')
      setIsScCoinHover(true)
    }
  }
  const headerWidth = location.pathname.includes('game-play')

  const handleOnMouseOut = (coinType) => {
    if (coinType === 'SC') {
      if (value === 0) {
        setIsScCoinHover(false)
      }
    } else {
      setIsGCHover(false)
    }
  }

  const onMouseOver = (coinType) => {
    if (coinType === 'SC') {
      if (value === 0) {
        setIsScCoinHover(true)
      }
    } else {
      setIsGCHover(true)
    }
  }

  const handleClickOpen = () => {
    setSearchDialog(true)
  }
  const [closeRibbon, setCloseRibbon] = useState(true)
  const handleClose = () => {
    setCloseRibbon(false)
  }
  const style = {
    '& .close-icon': {
      position: 'absolute',
      top: '1rem',
      right: '1rem',
      cursor: 'pointer',
      width: '0.875rem'
    }
  }
  const handlePackagePurchase = () => {
    if (isGamePlayActive) portalStore.openPortal(() => <InsufficientBalancePopup />, 'purchasePopupModal')
    else {
      navigate(PlayerRoutes.Store)
    }
  }
  console.log(!!getLoginToken(), 'Header auth State')

  return (
    <Grid className={classes.header} style={{ width: headerWidth ? '100%' : null }}>
      {/* {(socketRibbonData?.isRibbon  && closeRibbon )&& (
         <Box className='info-header' sx={style}>
         <Typography variant='body1'>
         {socketRibbonData?.startMessage}{' '}
           {countdown ? <Typography variant='span'>{formatTime(countdown)}</Typography> :""}   {socketRibbonData?.endMessage}
         </Typography>
         {socketRibbonData?.isCancelActive &&  <img className='close-icon' onClick={handleClose} src={closeIcon} />}
       </Box>
      )} */}
      <HeaderRibbon />
      <Grid className={`${classes.wrapper} ${isGamePlayActive ? 'game-play-header' : ''}`}>
        <Grid className='header-content'>
          <Grid className='header-left'>
            <Link
              className={`brand-logo ${isGamePlayActive ? '' : 'no-game-play'}`}
              onClick={() => navigate(PlayerRoutes.Lobby)}
            >
              <ImageRenderer src={logoData?.mobileLogo || BrandLogoMob} alt='The Money Factory mobile brand logo' />
            </Link>
            {/* <Box className='maintenance-wrap'>
              <Typography variant='body1'>We are undergoing maintenance in </Typography>
            </Box> */}
          </Grid>
          {isAllowedUserAccess === 'true' ? (
            <Grid className='header-right'>
              {!!getLoginToken() ? (
                <>
                  <Grid className='mobileMenu'>
                    <MobileMenu userDetails={userDetails} />
                  </Grid>
                  <Box className={classes.headerTabs}>
                    {value === 0
                      ? isScCoinHover && (
                          <Grid className='hoverTooltip'>
                            <Grid className='hoverParent'>
                              <Grid>
                                <img
                                  src={usdIcon1}
                                  style={{ width: '25px' }}
                                  alt={usdIcon1}
                                  height='100%'
                                  width='100%'
                                />
                              </Grid>
                              <Grid className='hoverText'>
                                <Typography className='hoverText1'>Sweep Coin mode is ON</Typography>
                                <Typography className='hoverText2'>
                                  SC offer a chance to win real prizes. "Win" can be redeemed. 1SC = 1USD
                                </Typography>
                              </Grid>
                            </Grid>
                          </Grid>
                        )
                      : isGCHover && (
                          <Grid className='hoverTooltip'>
                            <Grid className='hoverParent'>
                              <Grid>
                                <img
                                  src={coinIcon1}
                                  style={{ width: '25px' }}
                                  alt={coinIcon1}
                                  height='100%'
                                  width='100%'
                                />
                              </Grid>
                              <Grid className='hoverText'>
                                <Typography className='hoverText1'>Gold Coin mode is ON</Typography>
                                <Typography className='hoverText2'>GC can be used for fun play at any time</Typography>
                              </Grid>
                            </Grid>
                          </Grid>
                        )}
                    <Tabs value={coinType === 'SC' ? 0 : value} onChange={handleChange} aria-label='basic tabs example'>
                      <Tab
                        icon={
                          <div>
                            <img
                              src={usdIcon1}
                              style={{ width: '25px' }}
                              alt='walletIcon'
                              className='image1'
                              height='100%'
                              width='100%'
                            />
                            <Typography className=''>
                              <b>{`${formatPriceWithCommas(userDetails?.userWallet?.totalScCoin)} SC`}</b>
                            </Typography>
                          </div>
                        }
                        iconPosition='start'
                        // label={`Win - ${formatPriceWithCommas(userDetails?.userWallet?.scCoin?.wsc)} SC`}
                        label=' '
                        {...a11yProps(0)}
                        onMouseOut={() => handleOnMouseOut('SC')}
                        onMouseOver={() => onMouseOver('SC')}
                      />
                      <Tab
                        icon={
                          <div>
                            <img
                              src={coinIcon1}
                              style={{ width: '25px' }}
                              alt='coinIcon'
                              className='image1'
                              height='100%'
                              width='100%'
                            />
                          </div>
                        }
                        iconPosition='start'
                        label={`${formatPriceWithCommas(userDetails?.userWallet?.gcCoin)} GC`}
                        {...a11yProps(1)}
                        onMouseOut={() => handleOnMouseOut('GC')}
                        onMouseOver={() => onMouseOver('GC')}
                      />
                    </Tabs>
                  </Box>
                  <Grid className='buyBtn'>
                    <Button
                      variant='contained'
                      // onClick={() => navigate(PlayerRoutes.Store)}
                      onClick={handlePackagePurchase}
                      data-tracking='Header.Buy.Btn'
                    >
                      <img src={buyActiveIcon} alt='buy active icon' />
                      Buy
                    </Button>
                  </Grid>
                  <Button type='button' className='header-search' variant='outlined' onClick={handleClickOpen}>
                    <SearchIcon />
                  </Button>
                  <Grid className='profileBtn' onClick={() => navigate(PlayerRoutes.Account)}>
                    <img className='pointer-cursor' src={userHeaderIcon} alt='Header User' height='100%' width='100%' />
                  </Grid>
                </>
              ) : (
                <>
                  <Button variant='outlined' onClick={handleLoginOpen} className='login'>
                    Login
                  </Button>
                  <Button variant='contained' onClick={handleUsernameOpen} className='signup pulse'>
                    Sign up
                  </Button>
                </>
              )}
            </Grid>
          ) : (
            <></>
          )}
        </Grid>
      </Grid>
      <Search />
    </Grid>
  )
}

export default Header
